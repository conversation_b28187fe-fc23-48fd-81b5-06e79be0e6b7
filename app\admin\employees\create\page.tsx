"use client";

import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Plus, Trash2, User, FileText, CreditCard, GraduationCap, Briefcase, Users, Upload, Check } from "lucide-react";
import { useState } from "react";
import { useRouter } from "next/navigation";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { employeeSchema } from "@/app/api/employees/schema";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { cn } from "@/lib/utils";

type EmployeeForm = z.infer<typeof employeeSchema>;

export default function EmployeeForm() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const router = useRouter();
  
  const form = useForm<EmployeeForm>({
    resolver: zodResolver(employeeSchema),
    defaultValues: {
      isActive: true,
      educations: [{ instituteName: "", qualification: "" }],
      workExperiences: []
    },
  });

  const { fields: educationFields, append: appendEducation, remove: removeEducation } = useFieldArray({
    control: form.control,
    name: "educations"
  });

  const { fields: workFields, append: appendWork, remove: removeWork } = useFieldArray({
    control: form.control,
    name: "workExperiences"
  });

  const steps = [
    { title: "Personal Details", icon: User },
    { title: "Identification", icon: FileText },
    { title: "Bank Details", icon: CreditCard },
    { title: "Education", icon: GraduationCap },
    { title: "Experience", icon: Briefcase },
    { title: "Family Details", icon: Users },
  ];

  const progress = ((currentStep + 1) / steps.length) * 100;

  const uploadFile = async (file: File, fileType: string) => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('fileType', fileType);
    formData.append('userId', '1');

    const response = await fetch('/api/files/upload', {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      throw new Error('File upload failed');
    }

    const result = await response.json();
    return result.file.storageUrl;
  };

  async function onSubmit(values: EmployeeForm) {
    setIsSubmitting(true);
    try {
      const formElement = document.querySelector('form') as HTMLFormElement;
      const fileInputs = formElement.querySelectorAll('input[type="file"]') as NodeListOf<HTMLInputElement>;
      
      let profilePicUrl = '';
      let aadharCopyUrl = '';
      let panCopyUrl = '';
      let bankCopyUrl = '';
      let fatherAadharCopyUrl = '';
      let motherAadharCopyUrl = '';

      for (const input of Array.from(fileInputs)) {
        if (input.files && input.files.length > 0) {
          const file = input.files[0];

          // Try multiple ways to find the label
          let label = '';

          // Method 1: Use data attribute (most reliable)
          label = input.getAttribute('data-file-label') || '';

          // Method 2: Look for label in the same FormItem
          if (!label) {
            const formItem = input.closest('[data-file-type]');
            if (formItem) {
              label = formItem.getAttribute('data-file-type') || '';
            }
          }

          // Method 3: Look for label element
          if (!label) {
            const formItem = input.closest('[class*="FormItem"], .form-item');
            if (formItem) {
              const labelElement = formItem.querySelector('label');
              if (labelElement) {
                label = labelElement.textContent || '';
              }
            }
          }

          console.log('Processing file upload for label:', label);
          console.log('File name:', file.name);

          try {
            const url = await uploadFile(file, 'document');
            console.log('File uploaded successfully:', url);

            if (label.includes('Profile Photo')) {
              profilePicUrl = url;
              console.log('Set profilePicUrl:', url);
            } else if (label.includes('Aadhar Copy') && !label.includes('Father') && !label.includes('Mother')) {
              aadharCopyUrl = url;
              console.log('Set aadharCopyUrl:', url);
            } else if (label.includes('PAN Copy')) {
              panCopyUrl = url;
              console.log('Set panCopyUrl:', url);
            } else if (label.includes('Bank') && (label.includes('Copy') || label.includes('Passbook'))) {
              bankCopyUrl = url;
              console.log('Set bankCopyUrl:', url);
            } else if (label.includes("Father") && label.includes("Aadhar")) {
              fatherAadharCopyUrl = url;
              console.log('Set fatherAadharCopyUrl:', url);
            } else if (label.includes("Mother") && label.includes("Aadhar")) {
              motherAadharCopyUrl = url;
              console.log('Set motherAadharCopyUrl:', url);
            } else {
              console.warn('No matching label found for:', label);
            }
          } catch (error) {
            console.error('File upload failed:', error);
          }
        }
      }

      const formData = {
        ...values,
        profilePicUrl: profilePicUrl || undefined,
        aadharCopyUrl: aadharCopyUrl || undefined,
        panCopyUrl: panCopyUrl || undefined,
        bankCopyUrl: bankCopyUrl || undefined,
        fatherAadharCopyUrl: fatherAadharCopyUrl || undefined,
        motherAadharCopyUrl: motherAadharCopyUrl || undefined,
      };

      console.log('Form data being sent:', formData);
      console.log('File URLs:', {
        profilePicUrl,
        aadharCopyUrl,
        panCopyUrl,
        bankCopyUrl,
        fatherAadharCopyUrl,
        motherAadharCopyUrl
      });

      const response = await fetch('/api/employees', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to create employee');
      }

      const result = await response.json();
      console.log('Employee created:', result);
      
      form.reset();
      router.push('/admin/employees');
    } catch (error) {
      console.error('Error creating employee:', error);
      alert(error instanceof Error ? error.message : 'Failed to create employee');
    } finally {
      setIsSubmitting(false);
    }
  }

  const FileUploadField = ({ label, accept = "image/*,application/pdf", multiple = false }: { label: string; accept?: string; multiple?: boolean }) => (
    <FormItem data-file-type={label}>
      <FormLabel className="text-sm font-medium">{label}</FormLabel>
      <FormControl>
        <div className="relative">
          <Input
            type="file"
            accept={accept}
            multiple={multiple}
            data-file-label={label}
            className="file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-primary file:text-primary-foreground hover:file:bg-primary/90"
          />
          <Upload className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground pointer-events-none" />
        </div>
      </FormControl>
    </FormItem>
  );

  return (
    <div className="min-h-screen">
      <div className="container mx-auto px-4">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">

            {/* Personal Details */}
            <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <User className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <CardTitle className="text-xl text-gray-900">Personal Details</CardTitle>
                    <CardDescription>Basic information about the employee</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <FormField
                    control={form.control}
                    name="employeeId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium text-gray-700">Employee ID</FormLabel>
                        <FormControl>
                          <Input placeholder="EMP001" className="transition-all duration-200 focus:ring-2 focus:ring-blue-500" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="employeeName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium text-gray-700">Employee Name</FormLabel>
                        <FormControl>
                          <Input placeholder="John Doe" className="transition-all duration-200 focus:ring-2 focus:ring-blue-500" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium text-gray-700">Email Address</FormLabel>
                        <FormControl>
                          <Input type="email" placeholder="<EMAIL>" className="transition-all duration-200 focus:ring-2 focus:ring-blue-500" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FileUploadField label="Profile Photo" accept="image/*" />

                  <FormField
                    control={form.control}
                    name="dob"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium text-gray-700">Date of Birth</FormLabel>
                        <FormControl>
                          <Input type="date" className="transition-all duration-200 focus:ring-2 focus:ring-blue-500" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="mobileNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium text-gray-700">Mobile Number</FormLabel>
                        <FormControl>
                          <Input placeholder="9876543210" className="transition-all duration-200 focus:ring-2 focus:ring-blue-500" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="bloodGroup"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium text-gray-700">Blood Group</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger className="transition-all duration-200 focus:ring-2 focus:ring-blue-500">
                              <SelectValue placeholder="Select blood group" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {["A+", "A-", "B+", "B-", "AB+", "AB-", "O+", "O-"].map(group => (
                              <SelectItem key={group} value={group}>{group}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="maritalStatus"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium text-gray-700">Marital Status</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger className="transition-all duration-200 focus:ring-2 focus:ring-blue-500">
                              <SelectValue placeholder="Select marital status" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {["Single", "Married", "Divorced", "Widowed"].map(status => (
                              <SelectItem key={status} value={status}>{status}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="department"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium text-gray-700">Department</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger className="transition-all duration-200 focus:ring-2 focus:ring-blue-500">
                              <SelectValue placeholder="Select department" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {["IT", "HR", "Finance", "Marketing", "Operations", "Sales"].map(dept => (
                              <SelectItem key={dept} value={dept}>{dept}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="doj"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium text-gray-700">Date of Joining</FormLabel>
                        <FormControl>
                          <Input type="date" className="transition-all duration-200 focus:ring-2 focus:ring-blue-500" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="flex items-center justify-between p-4 rounded-lg border-2 border-dashed border-gray-200 bg-gray-50/50">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-green-100 rounded-full">
                      <Check className="h-4 w-4 text-green-600" />
                    </div>
                    <div>
                      <FormLabel className="text-base font-medium text-gray-900">Active Status</FormLabel>
                      <p className="text-sm text-gray-500">Employee is currently active and can access systems</p>
                    </div>
                  </div>
                  <FormField
                    control={form.control}
                    name="isActive"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>

                <Separator />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <FormField
                    control={form.control}
                    name="presentAddress"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium text-gray-700">Present Address</FormLabel>
                        <FormControl>
                          <Textarea 
                            placeholder="Enter current address..." 
                            className="min-h-[100px] transition-all duration-200 focus:ring-2 focus:ring-blue-500" 
                            {...field} 
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="permanentAddress"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium text-gray-700">Permanent Address</FormLabel>
                        <FormControl>
                          <Textarea 
                            placeholder="Enter permanent address..." 
                            className="min-h-[100px] transition-all duration-200 focus:ring-2 focus:ring-blue-500" 
                            {...field} 
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Identification Details */}
            <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <FileText className="h-6 w-6 text-purple-600" />
                  </div>
                  <div>
                    <CardTitle className="text-xl text-gray-900">Identification Details</CardTitle>
                    <CardDescription>Government issued identity documents</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <FormField
                    control={form.control}
                    name="aadhar"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium text-gray-700">Aadhar Number</FormLabel>
                        <FormControl>
                          <Input placeholder="1234 5678 9012" className="transition-all duration-200 focus:ring-2 focus:ring-blue-500" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FileUploadField label="Aadhar Copy" />
                </div>
                
                <Separator />
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <FormField
                    control={form.control}
                    name="pan"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium text-gray-700">PAN Number</FormLabel>
                        <FormControl>
                          <Input placeholder="**********" className="transition-all duration-200 focus:ring-2 focus:ring-blue-500" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />              
                  <FileUploadField label="PAN Copy" />
                </div>
              </CardContent>
            </Card>

            {/* Bank Details */}
            <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <CreditCard className="h-6 w-6 text-green-600" />
                  </div>
                  <div>
                    <CardTitle className="text-xl text-gray-900">Bank Details</CardTitle>
                    <CardDescription>Banking information for salary payments</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <FormField
                    control={form.control}
                    name="bankName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium text-gray-700">Bank Name</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger className="transition-all duration-200 focus:ring-2 focus:ring-blue-500">
                              <SelectValue placeholder="Select bank" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {[
                              { value: "SBI", label: "State Bank of India" },
                              { value: "HDFC", label: "HDFC Bank" },
                              { value: "ICICI", label: "ICICI Bank" },
                              { value: "Axis", label: "Axis Bank" },
                              { value: "PNB", label: "Punjab National Bank" },
                              { value: "BOI", label: "Bank of India" },
                              { value: "Canara", label: "Canara Bank" }
                            ].map(bank => (
                              <SelectItem key={bank.value} value={bank.value}>{bank.label}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="accountNo"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium text-gray-700">Account Number</FormLabel>
                        <FormControl>
                          <Input placeholder="**********" className="transition-all duration-200 focus:ring-2 focus:ring-blue-500" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="ifsc"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium text-gray-700">IFSC Code</FormLabel>
                        <FormControl>
                          <Input placeholder="SBIN0001234" className="transition-all duration-200 focus:ring-2 focus:ring-blue-500" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="branch"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium text-gray-700">Branch Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Main Branch" className="transition-all duration-200 focus:ring-2 focus:ring-blue-500" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FileUploadField label="Bank Passbook Copy" />
                </div>
              </CardContent>
            </Card>

            {/* Educational Details */}
            <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-orange-100 rounded-lg">
                      <GraduationCap className="h-6 w-6 text-orange-600" />
                    </div>
                    <div>
                      <CardTitle className="text-xl text-gray-900">Educational Details</CardTitle>
                      <CardDescription>Academic qualifications and certifications</CardDescription>
                    </div>
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => appendEducation({ instituteName: "", qualification: "" })}
                    className="flex items-center gap-2"
                  >
                    <Plus className="h-4 w-4" />
                    Add Education
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                {educationFields.map((field, index) => (
                  <div key={field.id} className="p-4 border border-gray-200 rounded-lg bg-gray-50/50">
                    <div className="flex justify-between items-center mb-4">
                      <Badge variant="secondary" className="text-sm">
                        Education {index + 1}
                      </Badge>
                      {educationFields.length > 1 && (
                        <Button
                          type="button"
                          variant="destructive"
                          size="sm"
                          onClick={() => removeEducation(index)}
                          className="h-8 w-8 p-0"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <FormField
                        control={form.control}
                        name={`educations.${index}.instituteName`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-medium text-gray-700">Institute Name</FormLabel>
                            <FormControl>
                              <Input placeholder="ABC University" className="transition-all duration-200 focus:ring-2 focus:ring-blue-500" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name={`educations.${index}.qualification`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-medium text-gray-700">Qualification</FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger className="transition-all duration-200 focus:ring-2 focus:ring-blue-500">
                                  <SelectValue placeholder="Select qualification" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {["10th", "12th", "Diploma", "B.Tech", "B.E", "BCA", "MCA", "MBA", "M.Tech", "PhD"].map(qual => (
                                  <SelectItem key={qual} value={qual}>{qual}</SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FileUploadField label="Certificates" multiple />
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Work Experience */}
            <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <Briefcase className="h-6 w-6 text-blue-600" />
                    </div>
                    <div>
                      <CardTitle className="text-xl text-gray-900">Work Experience</CardTitle>
                      <CardDescription>Previous employment history and experience</CardDescription>
                    </div>
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => appendWork({ previousOrganization: "", previousExperience: "", previousESI: "", previousUAN: "" })}
                    className="flex items-center gap-2"
                  >
                    <Plus className="h-4 w-4" />
                    Add Experience
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                {workFields.length === 0 ? (
                  <Alert>
                    <Briefcase className="h-4 w-4" />
                    <AlertDescription>
                      No work experience added yet. Click &quot;Add Experience&quot; to include previous employment details.
                    </AlertDescription>
                  </Alert>
                ) : (
                  workFields.map((field, index) => (
                    <div key={field.id} className="p-4 border border-gray-200 rounded-lg bg-gray-50/50">
                      <div className="flex justify-between items-center mb-4">
                        <Badge variant="secondary" className="text-sm">
                          Experience {index + 1}
                        </Badge>
                        <Button
                          type="button"
                          variant="destructive"
                          size="sm"
                          onClick={() => removeWork(index)}
                          className="h-8 w-8 p-0"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <FormField
                          control={form.control}
                          name={`workExperiences.${index}.previousOrganization`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-sm font-medium text-gray-700">Previous Organization</FormLabel>
                              <FormControl>
                                <Input placeholder="Company Name" className="transition-all duration-200 focus:ring-2 focus:ring-blue-500" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name={`workExperiences.${index}.previousESI`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-sm font-medium text-gray-700">Previous ESI No</FormLabel>
                              <FormControl>
                                <Input placeholder="ESI Number (optional)" className="transition-all duration-200 focus:ring-2 focus:ring-blue-500" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name={`workExperiences.${index}.previousUAN`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-sm font-medium text-gray-700">Previous UAN</FormLabel>
                              <FormControl>
                                <Input placeholder="UAN Number (optional)" className="transition-all duration-200 focus:ring-2 focus:ring-blue-500" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name={`workExperiences.${index}.previousExperience`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-sm font-medium text-gray-700">Experience Duration</FormLabel>
                              <Select onValueChange={field.onChange} defaultValue={field.value}>
                                <FormControl>
                                  <SelectTrigger className="transition-all duration-200 focus:ring-2 focus:ring-blue-500">
                                    <SelectValue placeholder="Select experience duration" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {["0-1 years", "1-2 years", "2-3 years", "3-5 years", "5-10 years", "10+ years"].map(exp => (
                                    <SelectItem key={exp} value={exp}>{exp}</SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FileUploadField label="Experience Certificates" multiple />
                      </div>
                    </div>
                  ))
                )}
              </CardContent>
            </Card>

            {/* Family Details */}
            <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-pink-100 rounded-lg">
                    <Users className="h-6 w-6 text-pink-600" />
                  </div>
                  <div>
                    <CardTitle className="text-xl text-gray-900">Family Details</CardTitle>
                    <CardDescription>Information about family members</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-6">
                  <div>
                    <h4 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      Father&apos;s Details
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <FormField
                        control={form.control}
                        name="fatherName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-medium text-gray-700">Father&apos;s Name</FormLabel>
                            <FormControl>
                              <Input placeholder="Enter father's full name" className="transition-all duration-200 focus:ring-2 focus:ring-blue-500" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="fatherAadhar"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-medium text-gray-700">Father&apos;s Aadhar</FormLabel>
                            <FormControl>
                              <Input placeholder="1234 5678 9012" className="transition-all duration-200 focus:ring-2 focus:ring-blue-500" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FileUploadField label="Father&apos;s Aadhar Copy" />
                    </div>
                  </div>

                  <Separator />

                  <div>
                    <h4 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                      <div className="w-2 h-2 bg-pink-500 rounded-full"></div>
                      Mother&apos;s Details
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <FormField
                        control={form.control}
                        name="motherName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-medium text-gray-700">Mother&apos;s Name</FormLabel>
                            <FormControl>
                              <Input placeholder="Enter mother's full name" className="transition-all duration-200 focus:ring-2 focus:ring-blue-500" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="motherAadhar"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-medium text-gray-700">Mother&apos;s Aadhar</FormLabel>
                            <FormControl>
                              <Input placeholder="1234 5678 9012" className="transition-all duration-200 focus:ring-2 focus:ring-blue-500" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FileUploadField label="Mother&apos;s Aadhar Copy" />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Submit Button */}
            <div className="flex justify-center pt-8">
              <Button 
                type="submit" 
                size="lg"
                disabled={isSubmitting}
                className="px-12 py-4 text-lg font-semibold bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 transition-all duration-200 transform hover:scale-105 shadow-lg"
              >
                {isSubmitting ? (
                  <div className="flex items-center gap-2">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                    Saving Employee...
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <Check className="h-5 w-5" />
                    Save Employee
                  </div>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
}