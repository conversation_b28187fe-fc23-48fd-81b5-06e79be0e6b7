import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Edit, Trash2, ArrowLeft } from "lucide-react"
import Link from "next/link"
import prisma from "@/lib/prisma"

async function getEmployee(id: string) {
  try {
    const employee = await prisma.employee.findUnique({
      where: { id },
      include: {
        educations: true,
        workExperiences: true,
      }
    });
    
    if (!employee) {
      throw new Error('Employee not found');
    }
    
    return employee;
  } catch (error) {
    console.error('Error fetching employee:', error);
    throw new Error('Failed to fetch employee');
  }
}

export default async function ViewEmployeePage({ params }: { params: { id: string } }) {
  const employee = await getEmployee(params.id);

  return (
    <div className="h-full overflow-y-auto">
      <div className="max-w-4xl mx-auto space-y-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <Link href="/admin/employees">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          </Link>
        </div>
        <div className="flex gap-2">
          <Link href={`/admin/employees/${employee.id}/edit`}>
            <Button variant="outline">
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Button>
          </Link>
          <Button variant="destructive">
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </Button>
        </div>
      </div>

      <div className="grid gap-6">
        {/* Personal Info */}
        <Card>
          <CardHeader>
            <CardTitle>Personal Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-start gap-6">
              <Avatar className="h-24 w-24">
                <AvatarImage 
                  src={employee.profilePicUrl || undefined} 
                  alt={employee.employeeName} 
                  className="object-cover" 
                />
                <AvatarFallback className="text-lg">
                  {employee.employeeName.split(' ').map(n => n[0]).join('').toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <div className="grid grid-cols-2 gap-4 flex-1">
                <div>
                  <label className="text-sm font-medium text-gray-500">Employee Name</label>
                  <p className="text-lg font-semibold">{employee.employeeName}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Employee ID</label>
                  <p className="text-lg">{employee.employeeId}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Email</label>
                  <p>{employee.email || 'N/A'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Mobile Number</label>
                  <p>{employee.mobileNumber}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Blood Group</label>
                  <Badge variant="outline">{employee.bloodGroup || 'N/A'}</Badge>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Date of Birth</label>
                  <p>{new Date(employee.dob).toLocaleDateString()}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Date of Joining</label>
                  <p>{new Date(employee.doj).toLocaleDateString()}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Marital Status</label>
                  <p>{employee.maritalStatus || 'N/A'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Department</label>
                  <p>{employee.department || 'N/A'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Status</label>
                  <Badge variant={employee.isActive ? "default" : "secondary"} className={employee.isActive ? "bg-green-100 text-green-800 border-green-200" : "bg-red-100 text-red-800 border-red-200"}>
                    {employee.isActive ? "Active" : "Inactive"}
                  </Badge>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Address */}
        <Card>
          <CardHeader>
            <CardTitle>Address Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-500">Present Address</label>
                <p>{employee.presentAddress || 'N/A'}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Permanent Address</label>
                <p>{employee.permanentAddress || 'N/A'}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Documents */}
        <Card>
          <CardHeader>
            <CardTitle>Documents</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-500">Aadhar Number</label>
                <p>{employee.aadhar}</p>
                {employee.aadharCopyUrl && (
                  <a href={employee.aadharCopyUrl} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline text-sm">
                    View Aadhar Copy
                  </a>
                )}
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">PAN Number</label>
                <p>{employee.pan}</p>
                {employee.panCopyUrl && (
                  <a href={employee.panCopyUrl} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline text-sm">
                    View PAN Copy
                  </a>
                )}
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Father's Aadhar</label>
                <p>{employee.fatherAadhar || 'N/A'}</p>
                {employee.fatherAadharCopyUrl && (
                  <a href={employee.fatherAadharCopyUrl} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline text-sm">
                    View Father's Aadhar Copy
                  </a>
                )}
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Mother's Aadhar</label>
                <p>{employee.motherAadhar || 'N/A'}</p>
                {employee.motherAadharCopyUrl && (
                  <a href={employee.motherAadharCopyUrl} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline text-sm">
                    View Mother's Aadhar Copy
                  </a>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Bank Details */}
        <Card>
          <CardHeader>
            <CardTitle>Bank Details</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-500">Bank Name</label>
                <p>{employee.bankName || 'N/A'}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Account Number</label>
                <p>{employee.accountNo || 'N/A'}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">IFSC Code</label>
                <p>{employee.ifsc || 'N/A'}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Branch</label>
                <p>{employee.branch || 'N/A'}</p>
              </div>
            </div>
            {employee.bankCopyUrl && (
              <div className="mt-4">
                <a href={employee.bankCopyUrl} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline text-sm">
                  View Bank Document
                </a>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Education */}
        {employee.educations && employee.educations.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Education</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {employee.educations.map((edu, index) => (
                  <div key={edu.id} className="border rounded-lg p-4">
                    <h4 className="font-semibold">Education {index + 1}</h4>
                    <div className="grid grid-cols-2 gap-4 mt-2">
                      <div>
                        <label className="text-sm font-medium text-gray-500">Institute</label>
                        <p>{edu.instituteName}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Qualification</label>
                        <p>{edu.qualification}</p>
                      </div>
                    </div>
                    {edu.certificateUrl && (
                      <div className="mt-2">
                        <a href={edu.certificateUrl} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline text-sm">
                          View Certificate
                        </a>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Work Experience */}
        {employee.workExperiences && employee.workExperiences.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Work Experience</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {employee.workExperiences.map((exp, index) => (
                  <div key={exp.id} className="border rounded-lg p-4">
                    <h4 className="font-semibold">Experience {index + 1}</h4>
                    <div className="grid grid-cols-2 gap-4 mt-2">
                      <div>
                        <label className="text-sm font-medium text-gray-500">Organization</label>
                        <p>{exp.previousOrganization}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Experience</label>
                        <p>{exp.previousExperience}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Previous ESI</label>
                        <p>{exp.previousESI || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Previous UAN</label>
                        <p>{exp.previousUAN || 'N/A'}</p>
                      </div>
                    </div>
                    {exp.experienceCertUrl && (
                      <div className="mt-2">
                        <a href={exp.experienceCertUrl} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline text-sm">
                          View Experience Certificate
                        </a>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Family Details */}
        {(employee.fatherName || employee.motherName) && (
          <Card>
            <CardHeader>
              <CardTitle>Family Details</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Father's Name</label>
                  <p>{employee.fatherName || 'N/A'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Mother's Name</label>
                  <p>{employee.motherName || 'N/A'}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
      </div>
    </div>
  );
}