import { employeeColumns } from "./columns"
import { ReusableDataTable } from "@/components/data-table"
import prisma from "@/lib/prisma";
import { Employee } from "@/types/admin"

async function getEmployees(): Promise<Employee[]> {
  try {
    const employees = await prisma.employee.findMany({
      select: {
        id: true,
        employeeId: true,
        employeeName: true,
        email: true,
        isActive: true,
        department: true,
        mobileNumber: true,
        doj: true,
        createdAt: true,
        updatedAt: true,
        profilePicUrl: true,
      },
      orderBy: {
        employeeName: 'asc'
      }
    });
    return employees;
  } catch (error) {
    console.error('Error fetching employees:', error);
    return [];
  }
}

export default async function EmployeesPage() {
  const employees = await getEmployees()
  return (
    <div className="h-full flex flex-col">
      <div className="flex-1 overflow-hidden">
        <ReusableDataTable
          columns={employeeColumns}
          data={employees}
          searchKey="employeeName"
          searchPlaceholder="Search employees..."
          showColumnToggle={true}
          showPagination={true}
          showSearch={true}
          pageSize={10}
          createLink="/admin/employees/create"
          createButtonLabel="Add Employee"
        />
      </div>
    </div>
  )
}