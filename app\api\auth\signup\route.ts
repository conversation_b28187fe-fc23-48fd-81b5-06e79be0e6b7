// app/api/auth/signup/route.ts (Next.js App Router)
import { supabase } from "@/lib/supabase";
import { prisma } from "@/lib/prisma";
import { z } from "zod";
import { NextResponse } from "next/server";

const signupSchema = z.object({
  email: z.string().email(),
  password: z.string().min(6),
  phone: z.string().min(10),
});

export async function POST(req: Request) {
  const body = await req.json();
  const parsed = signupSchema.safeParse(body);

  if (!parsed.success) {
    return  NextResponse.json({ error: parsed.error.format() }, { status: 400 });
  }

  const { email, password, phone } = parsed.data;

  // Supabase signup
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    phone,
    options: {
      data: {
        role: "USER",
      },
    },
  });

  if (error) return  NextResponse.json({ error: error.message }, { status: 400 });

  // Sync with Prisma DB
  if (data.user?.id) {
    await prisma.user.create({
      data: {
        supabaseId: data.user.id,
        email,
        phone,
        role: "USER",
      },
    });
  }

  return NextResponse.json({ user: data.user });
}
